#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
检查Nav2服务状态的诊断脚本
"""

import rclpy
from rclpy.node import Node
import time


class Nav2ServiceChecker(Node):
    def __init__(self):
        super().__init__('nav2_service_checker')
        
        # 需要检查的服务列表
        self.required_services = [
            '/compute_path_to_pose',
            '/smooth_path',
            '/follow_path',
            '/navigate_to_pose',
            '/navigate_through_poses',
            '/backup',
            '/spin',
            '/wait',
            '/drive_on_heading',
            '/assisted_teleop',
        ]
        
        # 需要检查的action服务列表
        self.required_action_services = [
            '/compute_path_to_pose/_action/status',
            '/compute_path_to_pose/_action/feedback',
            '/compute_path_to_pose/_action/get_result',
            '/compute_path_to_pose/_action/cancel_goal',
            '/follow_path/_action/status',
            '/follow_path/_action/feedback',
            '/follow_path/_action/get_result',
            '/follow_path/_action/cancel_goal',
        ]
        
        self.get_logger().info("Nav2服务检查器已启动")
        
    def check_services(self):
        """检查所有必需的服务"""
        self.get_logger().info("开始检查Nav2服务...")
        
        # 检查常规服务
        available_services = []
        missing_services = []
        
        service_names = self.get_service_names_and_types()
        available_service_names = [name for name, _ in service_names]
        
        for service in self.required_services:
            if service in available_service_names:
                available_services.append(service)
                self.get_logger().info(f"✅ 服务可用: {service}")
            else:
                missing_services.append(service)
                self.get_logger().warn(f"❌ 服务缺失: {service}")
        
        # 检查action服务
        for action_service in self.required_action_services:
            if action_service in available_service_names:
                available_services.append(action_service)
                self.get_logger().info(f"✅ Action服务可用: {action_service}")
            else:
                missing_services.append(action_service)
                self.get_logger().warn(f"❌ Action服务缺失: {action_service}")
        
        # 总结
        self.get_logger().info(f"服务检查完成:")
        self.get_logger().info(f"  可用服务: {len(available_services)}")
        self.get_logger().info(f"  缺失服务: {len(missing_services)}")
        
        if missing_services:
            self.get_logger().error("以下服务缺失，可能导致导航失败:")
            for service in missing_services:
                self.get_logger().error(f"  - {service}")
        else:
            self.get_logger().info("🎉 所有必需的服务都可用!")
        
        return len(missing_services) == 0
    
    def check_nodes(self):
        """检查Nav2节点状态"""
        self.get_logger().info("检查Nav2节点...")
        
        required_nodes = [
            '/controller_server',
            '/planner_server',
            '/smoother_server',
            '/behavior_server',
            '/bt_navigator',
            '/waypoint_follower',
            '/velocity_smoother',
            '/collision_monitor',
            '/lifecycle_manager_navigation',
        ]
        
        node_names = self.get_node_names()
        
        available_nodes = []
        missing_nodes = []
        
        for node in required_nodes:
            node_name = node.lstrip('/')  # 移除前导斜杠
            if node_name in node_names:
                available_nodes.append(node)
                self.get_logger().info(f"✅ 节点运行: {node}")
            else:
                missing_nodes.append(node)
                self.get_logger().warn(f"❌ 节点缺失: {node}")
        
        self.get_logger().info(f"节点检查完成:")
        self.get_logger().info(f"  运行节点: {len(available_nodes)}")
        self.get_logger().info(f"  缺失节点: {len(missing_nodes)}")
        
        return len(missing_nodes) == 0


def main():
    rclpy.init()
    
    checker = Nav2ServiceChecker()
    
    try:
        # 等待一段时间让系统稳定
        checker.get_logger().info("等待5秒让系统稳定...")
        time.sleep(5.0)
        
        # 检查节点
        nodes_ok = checker.check_nodes()
        
        # 等待一下
        time.sleep(2.0)
        
        # 检查服务
        services_ok = checker.check_services()
        
        if nodes_ok and services_ok:
            checker.get_logger().info("🎉 Nav2系统检查通过!")
            exit_code = 0
        else:
            checker.get_logger().error("❌ Nav2系统检查失败!")
            exit_code = 1
            
    except KeyboardInterrupt:
        checker.get_logger().info("检查被用户中断")
        exit_code = 1
    finally:
        checker.destroy_node()
        rclpy.shutdown()
        exit(exit_code)


if __name__ == '__main__':
    main()
