#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
测试单独启动导航系统
"""

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription, SetEnvironmentVariable
from launch.launch_description_sources import PythonLaunchDescriptionSource
from nav2_common.launch import RewrittenYaml


def generate_launch_description():
    # 获取包目录
    nav2_bringup_dir = get_package_share_directory('nav2_bringup')
    launch_dir = os.path.dirname(os.path.realpath(__file__))
    
    # 参数文件配置
    params_file = os.path.join(launch_dir, 'nav2_no_map_params.yaml')
    configured_params = RewrittenYaml(
        source_file=params_file, 
        root_key='', 
        param_rewrites='', 
        convert_types=True
    )

    # Nav2导航启动
    nav2_navigation = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(nav2_bringup_dir, 'launch', 'navigation_launch.py')
        ),
        launch_arguments={
            'namespace': '',
            'use_sim_time': 'True',
            'params_file': configured_params,
            'use_composition': 'False',
            'autostart': 'True',  # 自动启动
        }.items(),
    )

    return LaunchDescription([
        # 设置日志环境变量
        SetEnvironmentVariable('RCUTILS_LOGGING_BUFFERED_STREAM', '1'),
        SetEnvironmentVariable('RCUTILS_LOGGING_USE_STDOUT', '1'),
        
        # 导航系统
        nav2_navigation,
    ])


if __name__ == '__main__':
    generate_launch_description()
